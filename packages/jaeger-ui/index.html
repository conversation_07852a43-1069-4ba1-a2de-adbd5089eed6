<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- prevent caching of this HTML by any server, Go or otherwise -->
    <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />

    <!-- NOTE: The document MUST have a <base> element. package.json#homepage is set to "." as part of resolving https://github.com/jaegertracing/jaeger-ui/issues/42 and therefore static assets are linked via relative URLs. This will break on many document URLs, e.g. /trace/abc, unless a valid base URL is provided. The base href defaults to "/" but the query-service can inject an override. -->
    <base href="/" data-inject-target="BASE_URL" />
    <link rel="shortcut icon" href="/favicon.ico">
    <title>Jaeger UI</title>
    <script>
      // Jaeger UI config data is embedded by the query-service via search-replace.
      // Please see ./README.md#configuration for details.

      // TODO the JSON/JS bifurcation below could be avoided by using a single template function like:
      // function getJaegerUiConfig() { return null; }

      // Important! Do not alter the following line; query-service looks for that exact pattern.
      // JAEGER_CONFIG_JS

      function getJaegerUiConfig() {
        if(typeof window.UIConfig === 'function') {
          return UIConfig();
        }
        const DEFAULT_CONFIG = null;
        // Important! Do not alter the following line; query-service looks for that exact pattern.
        const JAEGER_CONFIG = DEFAULT_CONFIG;
        return JAEGER_CONFIG;
      }
      // Jaeger storage compabilities data is embedded by the query-service via search-replace.
      function getJaegerStorageCapabilities() {
        const DEFAULT_STORAGE_CAPABILITIES = { "archiveStorage": false };
        const JAEGER_STORAGE_CAPABILITIES = DEFAULT_STORAGE_CAPABILITIES;
        return JAEGER_STORAGE_CAPABILITIES;
      }
      // Jaeger version data is embedded by the query-service via search/replace.
      function getJaegerVersion() {
        const DEFAULT_VERSION = {'gitCommit':'', 'gitVersion':'', 'buildDate':''};
        // Important! Do not alter the following line; query-service looks for that exact pattern.
        const JAEGER_VERSION = DEFAULT_VERSION;
        return JAEGER_VERSION;
      }

      // Workaround some legacy NPM dependencies that assume this is always defined.
      window.global = {};
    </script>
  </head>
  <body>
    <div id="jaeger-ui-root"></div>
    <!--
      This file is the main entry point for the Jaeger UI application.
      See https://vitejs.dev/guide/#index-html-and-project-root for more information
      on how asset references are managed by the build system.
    -->
    <script type="module" src="/src/index.jsx"></script>
  </body>
</html>
