// Copyright (c) 2019 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// This file is necessary because the build system (Vite) requires
// isolatedModules to be true but for linting we need it to be false. We run
// tsc-lint from the project root, but packages have their own tsconfig for
// the lint so the typings from one package doesn't pollute other packages.
// For that to work, project references are used
// (https://www.typescriptlang.org/docs/handbook/project-references.html) which
// requires `composite: true` and `isolatedModules: false`.
// Since project references use emitted declarations to determine whether the project
// is up to date, a single index.d.ts file is specified as the output.
{
  "extends": "./tsconfig",
  "compilerOptions": {
    "isolatedModules": false,
    "composite": true,
    "outFile": "index.d.ts"
  },
  "files": [
    "src/actions/jaeger-api.js",
    "src/api/jaeger.js",
    "src/components/App/index.jsx",
    "src/components/DependencyGraph/index.tsx",
    "src/components/SearchTracePage/index.jsx",
    "src/components/SearchTracePage/SearchForm.jsx",
    "src/components/SearchTracePage/SearchForm.markers.js",
    "src/components/SearchTracePage/SearchResults/ScatterPlot.jsx",
    "src/components/SearchTracePage/SearchResults/index.markers.js",
    "src/components/SearchTracePage/SearchResults/ResultItem.markers.js",
    "src/middlewares/index.js",
    "src/model/order-by.js",
    "src/propTypes/dependencies.js",
    "src/reducers/config.js",
    "src/reducers/dependencies.js",
    "src/reducers/index.js",
    "src/reducers/services.js",
    "src/reducers/trace.js",
    "src/utils/configure-store.js",
    "src/utils/sort.js",
    "src/components/DependencyGraph/sample_data/large.json",
    "src/components/DependencyGraph/sample_data/small.json",
    "package.json"
  ]
}
