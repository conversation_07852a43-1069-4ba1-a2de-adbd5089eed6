// Copyright (c) 2023 The Jaeger Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import * as getConfig from './get-config';
import { getTargetBlankOrTop, getTargetEmptyOrBlank } from './get-target';

let getConfigValueSpy;

beforeAll(() => {
  getConfigValueSpy = jest.spyOn(getConfig, 'getConfigValue');
});

describe('getTarget', () => {
  it('getTargetEmptyOrBlank returns empty because forbidNewPage is true', () => {
    getConfigValueSpy.mockReturnValue(true);
    const target = getTargetEmptyOrBlank();
    expect(target).toBe('');
  });
  it('getTargetEmptyOrBlank returns _blank because forbidNewPage is true', () => {
    getConfigValueSpy.mockReturnValue(false);
    const target = getTargetEmptyOrBlank();
    expect(target).toBe('_blank');
  });
  it('getTargetBlankOrTop returns _top because forbidNewPage is true', () => {
    getConfigValueSpy.mockReturnValue(true);
    const target = getTargetBlankOrTop();
    expect(target).toBe('_top');
  });
  it('getTargetBlankOrTop returns _blank because forbidNewPage is true', () => {
    getConfigValueSpy.mockReturnValue(false);
    const target = getTargetBlankOrTop();
    expect(target).toBe('_blank');
  });
});
