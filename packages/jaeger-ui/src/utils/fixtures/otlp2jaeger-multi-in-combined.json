{"resourceSpans": [{"resource": {"attributes": [{"key": "service.name", "value": {"stringValue": "example-trace"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "nodejs"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.22.0"}}]}, "scopeSpans": [{"scope": {"name": "ohboy"}, "spans": [{"traceId": "c620759e5d60fafb8ee0922b30e06bc6", "spanId": "26f5ef9dbb885479", "parentSpanId": "49b8e9efa1e8a3b1", "name": "ohboy.do", "kind": 1, "startTimeUnixNano": "1711138246978000000", "endTimeUnixNano": "1711138247024460625", "status": {}}]}]}, {"resource": {"attributes": [{"key": "service.name", "value": {"stringValue": "example-trace"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "nodejs"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.22.0"}}]}, "scopeSpans": [{"scope": {"name": "ohboy"}, "spans": [{"traceId": "c620759e5d60fafb8ee0922b30e06bc6", "spanId": "e76628f4e6dde174", "parentSpanId": "49b8e9efa1e8a3b1", "name": "ohboy.do", "kind": 1, "startTimeUnixNano": "1711138247025000000", "endTimeUnixNano": "1711138247096355250", "status": {}}]}]}, {"resource": {"attributes": [{"key": "service.name", "value": {"stringValue": "example-trace"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "nodejs"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.22.0"}}]}, "scopeSpans": [{"scope": {"name": "widgets", "version": "1.2.3"}, "spans": [{"traceId": "c620759e5d60fafb8ee0922b30e06bc6", "spanId": "49b8e9efa1e8a3b1", "parentSpanId": "eff3153be6b1fb93", "name": "info", "kind": 1, "startTimeUnixNano": "1711138246943000000", "endTimeUnixNano": "1711138247116150875", "status": {}}]}]}, {"resource": {"attributes": [{"key": "service.name", "value": {"stringValue": "example-trace"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "nodejs"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.22.0"}}]}, "scopeSpans": [{"scope": {"name": "widgets", "version": "1.2.3"}, "spans": [{"traceId": "c620759e5d60fafb8ee0922b30e06bc6", "spanId": "eff3153be6b1fb93", "parentSpanId": "", "name": "main", "kind": 1, "startTimeUnixNano": "1711138246940000000", "endTimeUnixNano": "1711138247116321958", "status": {}}]}]}]}