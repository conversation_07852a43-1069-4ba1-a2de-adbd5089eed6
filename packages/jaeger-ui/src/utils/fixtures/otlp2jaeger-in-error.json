{"resourceSpans": [{"resource": {"attributes": []}, "scopeSpans": [{"scope": {"name": "telemetrygen"}, "spans": [{"traceId": "83a9efd15c1c98a977e0711cc93ee28b", "spanId": "e127af99e3b3e074", "parentSpanId": "909541b92cf05311", "name": "okey-dokey-0", "kind": 2, "startTimeUnixNano": "1706678909209712000", "endTimeUnixNano": "1706678909209835000", "attributes": [{"key": "net.peer.ip", "value": {"stringValue": "*******"}}, {"key": "peer.service", "value": {"stringValue": "telemetrygen-client"}}], "status": {}}]}], "schemaUrl": "https://opentelemetry.io/schemas/1.4.0"}]}