// Copyright (c) 2020 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React, { useMemo, useEffect, useCallback } from 'react';
import { Modal, Table } from 'antd';
import { IoExitOutline, IoInformationCircleOutline } from 'react-icons/io5';

import { TDdgVertex } from '../../../model/ddg/types';
import { TPathAgnosticDecorationSchema } from '../../../model/path-agnostic-decorations/types';
import { getConfigValue } from '../../../utils/config/get-config';
import DetailsPanel from './DetailsPanel';
import * as track from './index.track';

import './index.css';

type TProps = {
  clearSelected: () => void;
  selectDecoration: (decoration?: string) => void;
  selectedDecoration?: string;
  selectedVertex?: TDdgVertex;
};

const SidePanel: React.FC<TProps> = props => {
  const { clearSelected, selectDecoration, selectedDecoration, selectedVertex } = props;

  const decorations: TPathAgnosticDecorationSchema[] | undefined = useMemo(
    () => getConfigValue('pathAgnosticDecorations'),
    []
  );

  useEffect(() => {
    track.trackDecorationSelected(selectedDecoration);
  }, [selectedDecoration]);

  useEffect(() => {
    track.trackDecorationViewDetails(selectedVertex);
  }, [selectedVertex]);

  const handleClearSelected = useCallback(() => {
    track.trackDecorationViewDetails();
    clearSelected();
  }, [clearSelected]);

  const handleSelectDecoration = useCallback(
    (decoration?: string) => {
      selectDecoration(decoration);
    },
    [selectDecoration]
  );

  const handleOpenInfoModal = useCallback(() => {
    Modal.info({
      content: (
        <Table
          columns={[
            {
              dataIndex: 'acronym',
              key: 'acronym',
              title: 'Acronym',
            },
            {
              dataIndex: 'name',
              key: 'name',
              title: 'Name',
            },
          ]}
          dataSource={decorations}
          rowKey={(schema: TPathAgnosticDecorationSchema) => schema.id}
        />
      ),
      maskClosable: true,
      title: 'Decoration Options',
      width: '60vw',
    });
  }, [decorations]);

  if (!decorations) {
    return null;
  }

  const selectedSchema = decorations.find(
    ({ id }: TPathAgnosticDecorationSchema) => id === selectedDecoration
  );

  return (
    <div className="Ddg--SidePanel">
      <div className="Ddg--SidePanel--Btns">
        <button
          className={`Ddg--SidePanel--closeBtn ${selectedVertex && selectedSchema ? '' : 'is-hidden'}`}
          type="button"
          onClick={handleClearSelected}
        >
          <IoExitOutline />
        </button>
        <div className="Ddg--SidePanel--DecorationBtns">
          {decorations.map(({ acronym, id }: TPathAgnosticDecorationSchema) => (
            <button
              key={id}
              className={`Ddg--SidePanel--decorationBtn ${id === selectedDecoration ? 'is-selected' : ''}`}
              type="button"
              onClick={() => handleSelectDecoration(id)}
            >
              {acronym}
            </button>
          ))}
          <button
            key="clearBtn"
            className="Ddg--SidePanel--decorationBtn"
            type="button"
            onClick={() => handleSelectDecoration()}
          >
            Clear
          </button>
        </div>
        <button className="Ddg--SidePanel--infoBtn" onClick={handleOpenInfoModal} type="button">
          <IoInformationCircleOutline />
        </button>
      </div>
      <div className={`Ddg--SidePanel--Details ${selectedVertex && selectedSchema ? '.is-expanded' : ''}`}>
        {selectedVertex && selectedSchema && (
          <DetailsPanel
            decorationSchema={selectedSchema}
            operation={selectedVertex.operation}
            service={selectedVertex.service}
          />
        )}
      </div>
    </div>
  );
};

export default React.memo(SidePanel);
