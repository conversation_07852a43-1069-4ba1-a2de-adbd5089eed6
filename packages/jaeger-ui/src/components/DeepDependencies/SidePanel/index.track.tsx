// Copyright (c) 2020 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { trackEvent } from '../../../utils/tracking';

// export for tests
export const CATEGORY_DECORATION_SELECTION = 'jaeger/ux/ddg/decoration-selection';
export const CATEGORY_DECORATION_VIEW_DETAILS = 'jaeger/ux/ddg/decoration-view-details';

// export for tests
export const ACTION_CLEAR = 'clear';
export const ACTION_SET = 'set';

export function trackDecorationSelected(decoration?: string) {
  if (decoration) trackEvent(CATEGORY_DECORATION_SELECTION, ACTION_SET, decoration);
  else trackEvent(CATEGORY_DECORATION_SELECTION, ACTION_CLEAR);
}

export function trackDecorationViewDetails(value?: unknown) {
  if (value) trackEvent(CATEGORY_DECORATION_VIEW_DETAILS, ACTION_SET);
  else trackEvent(CATEGORY_DECORATION_VIEW_DETAILS, ACTION_CLEAR);
}
