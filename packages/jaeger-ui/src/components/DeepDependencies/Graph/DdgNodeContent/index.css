/*
Copyright (c) 2019 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

/*
 * Because .plexus-Digraph--MeasurableHtmlNode is `position: absolute`, adding a z-index to DdgNodeContent
 * would have no effect on layering between different nodes.
 */
.plexus-Digraph--MeasurableHtmlNode {
  /* transition-delay must equal .DdgNodeContent--actionsWrapper transition delay plus duration */
  transition-delay: 250ms;
  transition-property: z-index;
  z-index: 0;
}

.plexus-Digraph--MeasurableHtmlNode:hover {
  transition-delay: 0s;
  z-index: 1;
}

.DdgNodeContent--core {
  background: #eee;
  border-radius: 100%;
  box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.15);
  cursor: default;
  display: flex;
}

.DdgNodeContent--core.is-decorated {
  cursor: pointer;
}

.DdgNodeContent--core.is-missingDecoration {
  background-color: #e58c33;
}

.DdgNodeContent--core.is-positioned {
  bottom: 1px;
  left: 1px;
  position: absolute;
  right: 1px;
  top: 1px;
}

.DdgNodeContent--labelWrapper {
  margin-right: auto;
  margin-left: auto;
}

.DdgNodeContent--label {
  margin: 0;
  margin-right: auto;
  margin-left: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.DdgNodeContent--actionsWrapper {
  background: #fff;
  border-radius: 3px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  bottom: 100%;
  box-shadow: 0 0px 4px 1px rgba(0, 0, 0, 0.1);
  left: 1em;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  /* transition delay plus duration must equal .plexus-Digraph--MeasurableHtmlNode transition-delay  */
  transition-delay: 150ms;
  transition-duration: 0.1s;
  transition-property: opacity;
}

.DdgNodeContent:hover > .DdgNodeContent--actionsWrapper {
  opacity: 1;
  pointer-events: all;
  transition-delay: 0s;
}

.DdgNodeContent--actionsWrapper-below {
  bottom: auto !important;
  top: 100% !important;
  border-radius: 3px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  margin-top: 2px;
}
