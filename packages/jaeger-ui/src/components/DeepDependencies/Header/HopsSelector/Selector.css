/*
Copyright (c) 2019 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.HopsSelector--Selector {
  color: var(--tx-color-muted);
  margin: 0 0.5em;
  padding: 0px 3px;
}

.HopsSelector--Selector--decrement,
.HopsSelector--Selector--increment {
  background-color: #f4f4f4;
  border: none;
  cursor: pointer;
  height: 27px;
  padding: 0em 0.3em 0.3em;
  width: 27px;
}

.HopsSelector--Selector--decrement {
  margin-right: 0.75em;
}

.HopsSelector--Selector--increment {
  margin-left: 0.75em;
}

.HopsSelector--Selector--decrement:disabled,
.HopsSelector--Selector--increment:disabled {
  color: #bbb;
}

.HopsSelector--Selector--btn {
  background: transparent;
  border: none;
  color: #096dd9;
  cursor: pointer;
  font-weight: bold;
  margin: 0 0.2em;
  padding: 1px 2px;
}

.HopsSelector--Selector--btn.is-Empty {
  color: #aaa;
  font-weight: 400;
}

.HopsSelector--Selector--btn.is-Partial {
  color: #d46b08;
}

/* .HopsSelector--Selector > .HopsSelector--Selector--furthest {
  margin-left: 0.3em;
} */

.HopsSelector--Selector > .HopsSelector--Selector--delimiter {
  color: #999;
  font-weight: 400;
}

.HopsSelector--Selector--slash {
  color: #999;
}

.HopsSelector--Selector--AscIcon {
  margin-right: 0.3em;
}

.HopsSelector--Selector--AscIcon.is-Up {
  transform: scaleY(-1);
}

.HopsSelector--Selector--ChevronDown {
  color: #999;
  height: 0.7em;
  padding-bottom: 0.15em;
  width: 0.7em;
}

.HopsSelector--Selector--ChevronRight {
  color: #999;
  height: 0.7em;
  padding-bottom: 0.15em;
  width: 0.7em;
}

.HopsSelector--Selector--ChevronRight.is-Empty {
  color: #ccc;
}
