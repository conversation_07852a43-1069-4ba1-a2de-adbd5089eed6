/*
Copyright (c) 2019 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.DdgHeader {
  align-items: stretch;
  background: #fafafa;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
}

.DdgHeader--controlHeader {
  align-items: center;
  background-color: #f6f6f6;
  display: flex;
  justify-content: space-between;
  padding-left: 1.25rem;
}

.DdgHeader--paramsHeader {
  align-items: center;
  background: #fafafa;
  border-bottom: 1px solid #eee;
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  font-size: unset;
  margin: 0;
  padding: 0.75rem 1.25rem 0.75rem 1.25rem;
  position: relative;
}

.DdgHeader--findWrapper {
  display: flex;
  flex: 1 1 auto;
  justify-content: flex-end;
}

.DdgHeader--uiFind {
  align-items: center;
  background-color: #fff;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  color: #bbb;
  display: flex;
  flex-grow: 1;
  max-width: 400px;
  min-width: 200px;
  transition: max-width 0.5s;
}

.DdgHeader--uiFind:focus-within {
  max-width: 550px;
}

.DdgHeader--uiFindSearchIcon {
  margin: 0 0.25em;
  padding: 0.24em;
}

.DdgHeader--uiFindInput > input:focus,
.DdgHeader--uiFindInput > input {
  border: none;
  box-shadow: none;
  border-radius: 0px;
}

.DdgHeader--uiFindInfo {
  background: #ffffb8;
  border: 1px dashed #fadb14;
  border-radius: 4px;
  color: #ad8b00;
  margin-left: 0.25em;
  margin-right: 0.5em;
  padding: 0.27em 0.4em;
  stroke: #666;
  stroke-width: 1.2px;
  text-align: center;
  transition: width 0.5s;
  white-space: nowrap;
}

.DdgHeader--uiFindInfo--hidden {
  border-left: dashed var(--tx-color-muted) 1px;
  margin-left: 0.3em;
  padding-left: 0.3em;
}

.DdgHeader--uiFindInfo--icon {
  margin-bottom: 1.5px;
  margin-left: 0.2em;
}

.DdgHeader--uiFindInfo--tooltip {
  white-space: nowrap;
}
