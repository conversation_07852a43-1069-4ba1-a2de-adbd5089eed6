/*
Copyright (c) 2019 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.Ddg--LayoutSettings {
  margin-right: 3em;
}
.Ddg--LayoutSettings--optionsTable > tbody > tr {
  vertical-align: top;
}

.Ddg--LayoutSettings--optionsTable > tbody > tr > td {
  padding: 0.75em;
}

.Ddg--LayoutSettings--optionsTable > tbody:not(:first-child) > tr:first-child > td {
  padding-top: 1.5em;
}

.Ddg--LayoutSettings--optionGroupTitle {
  color: #999;
  font-size: 0.95em;
}

.Ddg--LayoutSettings--option {
  display: flex;
  max-width: 500px;
}

.Ddg--LayoutSettings--optionNote {
  color: var(--tx-color-title);
}

.Ddg--LayoutSettings--optionDescription {
  margin-top: -0.2em;
  white-space: normal;
}
