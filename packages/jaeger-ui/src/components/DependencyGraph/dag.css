/* Copyright (c) 2023 The Jaeger Authors

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

 http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License. */

.DAG {
  border: 1px solid #bbb;
  cursor: move;
  overflow: hidden;
  height: 100%;
  width: 100%;
  position: relative;
}

.DAG--dag {
  background: #fafafa;
  stroke-width: 0.7;
  stroke: #444;
}

.DAG--dag marker {
  stroke-width: 0.7;
  stroke: #444;
  fill: #444;
}

.DAG--node {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.DAG--nodeCircle {
  background-color: #dddddd;
  border-radius: 50%;
  height: 3rem;
  width: 3rem;
  margin-bottom: 1.75rem;
  border: 1px solid #bbb;
  box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.15);
}

.DAG--nodeCircle.is-focalNode {
  border: 2px solid #eb2f96;
  background-image: repeating-linear-gradient(30deg, transparent, #eb2f96 3px, transparent 5px);
}

.DAG--nodeCircle.is-match {
  background-image: repeating-linear-gradient(-60deg, #fffb8f, #fff566 3px, #fffb8f 5px);
  border: 1px dashed rgba(173, 139, 0, 0.5);
}

.DAG--nodeCircle.is-match.is-focalNode {
  background-image: repeating-linear-gradient(30deg, #fffb8f, #eb2f96 3px, #fffb8f 5px);
}

.DAG--nodeLabel {
  position: absolute;
  top: 3rem;
  text-align: center;
  font-weight: bold;
  font-size: 1.25rem;
  color: #7f7f7f;
  background-color: #fafafa;
}

.DAG--error {
  margin: 10px;
}

.DAG .NodeContent--actionsItemIconWrapper > * {
  position: static;
}

.DAG > div:last-child {
  position: fixed;
  z-index: 1000;
  background: #fafafa;
  border: 1px solid #d0d0d0;
}
