/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.SearchResults {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.SearchResults--header {
  border: 1px solid #e6e6e6;
  color: var(--tx-color-title);
  margin-bottom: 1.25rem;
}

.SearchResults--headerOverview {
  align-items: center;
  background-color: #f5f5f5;
  display: flex;
  padding: 1rem;
}

.SearchResults--headerScatterPlot {
  border-bottom: 1px solid #e6e6e6;
}

.SearchResults--resultLink {
  color: inherit;
}

.SearchResults--resultLink:hover {
  color: inherit;
}

.SearchResults--ddg-container {
  border: #e6e6e6 1px solid;
  flex-grow: 1;
  position: relative;
}
