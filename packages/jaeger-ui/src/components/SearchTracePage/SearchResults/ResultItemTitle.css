/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.ResultItemTitle {
  background: #ececec;
  border-bottom: 1px solid #d8d8d8;
  display: flex;
}

.ResultItemTitle--item {
  color: inherit;
  padding: 0.5rem;
  position: relative;
}

.ResultItemTitle--item:first-child {
  border-right: 1px solid #ddd;
}

.ResultItemTitle--item:hover {
  background: #e4e4e4;
  border-color: #ccc;
}

.ResultItemTitle--title {
  margin: 0;
  position: relative;
}

.ResultItemTitle--title.is-error {
  color: #c00;
}

.ResultItemTitle--idExcerpt {
  color: #888;
  font-weight: normal;
  padding-left: 0.5rem;
}

.ResultItemTitle--durationBar {
  background: #d7e7ea;
  bottom: 0;
  left: 0;
  position: absolute;
  top: 0;
}

.ResultItemTitle--item:hover > .ResultItemTitle--durationBar {
  background: #c5dde0;
}
