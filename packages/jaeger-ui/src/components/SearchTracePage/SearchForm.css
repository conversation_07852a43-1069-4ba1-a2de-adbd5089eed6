/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.SearchForm--labelCount {
  color: #999;
}

.SearchForm--hintTrigger {
  border: 1px solid #999;
  border-radius: 100px;
  color: #999;
  cursor: pointer;
  padding: 1px;
}

.SearchForm--hintTrigger:hover {
  color: #000;
  border-color: #000;
}

.SearchForm--tagsHintTitle {
  margin-top: 0.5em;
}

.SearchForm--submit {
  background-color: #11939a;
  color: white;
  float: right;
}

.SearchForm--submit:hover {
  background-color: #fff;
  float: right;
}

.SearchForm--submit:active {
  background-color: #fff;
  float: right;
}

.SearchForm--tagsHintInfo {
  padding-left: 1.7em;
}

.SearchForm--tagsHintEg {
  color: teal;
}
