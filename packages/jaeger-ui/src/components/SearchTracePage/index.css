/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.SearchTracePage--row {
  display: flex;
  flex-grow: 1;
}

.SearchTracePage--column {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 1rem 0.5rem;
}

.SearchTracePage--column:first-child {
  padding-left: 1rem;
}

.SearchTracePage--column:last-child {
  padding-right: 1rem;
}

.SearchTracePage--find {
  background-color: #f5f5f5;
  border: 1px solid #e6e6e6;
  padding: 1rem;
}

.SearchTracePage--logo {
  display: block;
  margin: 13rem auto 0 auto;
}
