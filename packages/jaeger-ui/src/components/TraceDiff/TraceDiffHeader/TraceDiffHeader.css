/*
Copyright (c) 2018 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.TraceDiffHeader {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex: 0;
  position: relative;
  z-index: 1;
  align-items: stretch;
}

.TraceDiffHeader--labelItem {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  background: #f5f5f5;
  border-right: 1px solid #e8e8e8;
}

.TraceDiffHeader--label {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.TraceDiffHeader--traceSection {
  display: flex;
  flex: 1;
  min-width: 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.TraceDiffHeader--traceSection:hover {
  background-color: #f9f9f9;
}

.TraceDiffHeader--divider {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  background: #f5f5f5;
  border-right: 1px solid #e8e8e8;
  border-left: 1px solid #e8e8e8;
}

.TraceDiffHeader--vsContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #199;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.TraceDiffHeader--vsContainer:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.TraceDiffHeader--vsLabel {
  font-size: 14px;
  font-weight: 700;
  color: white;
  letter-spacing: 1px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.TraceDiffHeader--popover .ant-popover-title {
  padding: 10px;
  border-bottom: 1px solid #e8e8e8;
}

.TraceDiffHeader--popover .ant-popover-inner-content {
  padding: 0;
}

.TraceDiffHeader--popover .ant-popover-inner {
  border-radius: 4px;
  box-shadow:
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}
