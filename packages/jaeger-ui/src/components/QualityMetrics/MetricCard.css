/*
Copyright (c) 2020 The Jaeger Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.MetricCard {
  border-radius: 7px;
  border: 1px rgba(0, 0, 0, 0.5) solid;
  display: flex;
  margin: 5px 5%;
}

.MetricCard--Body {
  border-left: solid 1px rgba(0, 0, 0, 0.3);
  box-shadow: -3px 0 3px rgba(0, 0, 0, 0.1);
  flex-grow: 1;
  padding: 5px;
  text-align: center;
}

.MetricCard--CircularProgressbarWrapper {
  flex-shrink: 0;
  max-width: 180px;
  padding: 5px;
  width: 25%;
}

.MetricCard--CountsWrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.MetricCard--Description {
  margin: 0 auto;
  width: 60%;
}

.MetricCard--Details {
  text-align: left;
  padding: 0.3em;
}

.MetricCard--TitleHeader {
  border-bottom: solid 1px #ddd;
  box-shadow: 0px 5px 5px -5px rgba(0, 0, 0, 0.3);
  font-size: 1.5em;
}
