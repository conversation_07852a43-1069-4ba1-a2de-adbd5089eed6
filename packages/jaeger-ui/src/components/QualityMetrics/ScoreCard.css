/*
Copyright (c) 2020 The Jaeger Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.ScoreCard {
  margin: 10px;
  max-width: 25%;
  min-width: 180px;
  text-align: center;
}

.ScoreCard--CircularProgressbarWrapper {
  padding: 3px;
}

.ScoreCard--TitleHeader {
  border-bottom: solid 1px #ddd;
  box-shadow: 0px 5px 5px -5px rgba(0, 0, 0, 0.3);
  font-size: 1.8em;
}
