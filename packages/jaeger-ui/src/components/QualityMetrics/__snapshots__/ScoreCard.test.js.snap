// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ScoreCard renders as expected when score is below max 1`] = `
<div
  className="ScoreCard"
>
  <span
    className="ScoreCard--TitleHeader"
  >
    Test Score
  </span>
  <div
    className="ScoreCard--CircularProgressbarWrapper"
  >
    <CircularProgressbar
      decorationHue={120}
      maxValue={108}
      text="38.9%"
      value={42}
    />
  </div>
  <a
    href="test.link"
    rel="noreferrer noopener"
    target="_blank"
  >
    How to improve 
    <NewWindowIcon />
  </a>
</div>
`;

exports[`ScoreCard renders as expected when score is max 1`] = `
<div
  className="ScoreCard"
>
  <span
    className="ScoreCard--TitleHeader"
  >
    Test Score
  </span>
  <div
    className="ScoreCard--CircularProgressbarWrapper"
  >
    <CircularProgressbar
      decorationHue={120}
      maxValue={108}
      text="100.0%"
      value={108}
    />
  </div>
  <a
    href="test.link"
    rel="noreferrer noopener"
    target="_blank"
  >
    Great! What does this mean 
    <NewWindowIcon />
  </a>
</div>
`;

exports[`ScoreCard renders as expected when score is zero 1`] = `
<div
  className="ScoreCard"
>
  <span
    className="ScoreCard--TitleHeader"
  >
    Test Score
  </span>
  <div
    className="ScoreCard--CircularProgressbarWrapper"
  >
    <CircularProgressbar
      backgroundHue={0}
      decorationHue={120}
      maxValue={108}
      text="0.0%"
      value={0}
    />
  </div>
  <a
    href="test.link"
    rel="noreferrer noopener"
    target="_blank"
  >
    How to improve 
    <NewWindowIcon />
  </a>
</div>
`;
