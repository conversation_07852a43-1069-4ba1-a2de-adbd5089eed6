// Copyright (c) 2020 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import * as React from 'react';

import ExamplesLink, { TExample } from '../common/ExamplesLink';

import './CountCard.css';

export type TProps = {
  count?: number;
  title?: string;
  examples?: TExample[];
};

const CountCard: React.FC<TProps> = ({ count, title, examples }) => {
  if (count === undefined || title === undefined) return null;

  return (
    <div className="CountCard">
      <span className="CountCard--TitleHeader">{title}</span>
      <span className="CountCard--Count">{count}</span>
      <ExamplesLink examples={examples} includeText />
    </div>
  );
};

export default React.memo(CountCard);
