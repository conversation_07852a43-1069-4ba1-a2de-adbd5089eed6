/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.detail-row-expanded-accent {
  cursor: pointer;
  height: 100%;
  overflow: hidden;
  position: absolute;
  width: 100%;
}

.detail-row-expanded-accent::before {
  border-left: 4px solid;
  pointer-events: none;
  width: 1000px;
}

.detail-row-expanded-accent::after {
  border-right: 1000px solid;
  border-color: inherit;
  cursor: pointer;
  opacity: 0.2;
}

/* border-color inherit must come AFTER other border declarations for accent */
.detail-row-expanded-accent::before,
.detail-row-expanded-accent::after {
  border-color: inherit;
  content: ' ';
  position: absolute;
  height: 100%;
}

.detail-row-expanded-accent:hover::after {
  opacity: 0.35;
}

.detail-info-wrapper {
  background: #f5f5f5;
  border: 1px solid #d3d3d3;
  border-top: 3px solid;
  padding: 0.75rem;
}
