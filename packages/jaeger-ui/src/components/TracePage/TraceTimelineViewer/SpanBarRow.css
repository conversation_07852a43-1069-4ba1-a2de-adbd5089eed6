/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.span-row.is-matching-filter {
  background-color: #fffce4;
}

.span-name-column {
  position: relative;
  white-space: nowrap;
  z-index: 1;
}

.span-name-column:hover {
  z-index: 1;
}

.span-row.clipping-left .span-name-column::before {
  content: ' ';
  height: 100%;
  position: absolute;
  width: 6px;
  background-image: linear-gradient(to right, rgba(25, 25, 25, 0.25), rgba(32, 32, 32, 0));
  left: 100%;
  z-index: -1;
}

.span-name-wrapper {
  background: #f8f8f8;
  line-height: 27px;
  overflow: hidden;
  display: flex;
}

.span-name-wrapper.is-matching-filter {
  background-color: #fffce4;
}

.span-name-wrapper:hover {
  border-right: 1px solid #bbb;
  float: left;
  min-width: calc(100% + 1px);
  overflow: visible;
}

.span-row:hover .span-name-wrapper {
  background: #f8f8f8;
  background: linear-gradient(90deg, #fafafa, #f8f8f8 75%, #eee);
}

.span-row.is-matching-filter:hover .span-name-wrapper {
  background: linear-gradient(90deg, #fff5e1, #fff5e1 75%, #ffe6c9);
}

.span-row.is-expanded .span-name-wrapper {
  background: #f0f0f0;
  box-shadow: 0 1px 0 #ddd;
}

.span-row.is-expanded .span-name-wrapper.is-matching-filter {
  background: #fff3d7;
}

.span-name {
  color: #000;
  cursor: pointer;
  flex: 1 1 auto;
  outline: none;
  overflow: hidden;
  padding-left: 4px;
  padding-right: 0.25em;
  position: relative;
  text-overflow: ellipsis;
}

.span-name::before {
  content: ' ';
  position: absolute;
  top: 4px;
  bottom: 4px;
  left: 0;
  border-left: 4px solid;
  border-left-color: inherit;
}

.span-name.is-detail-expanded::before {
  bottom: 0;
}

/* This is so the hit area of the span-name extends the rest of the width of the span-name column */
.span-name::after {
  background: transparent;
  bottom: 0;
  content: ' ';
  left: 0;
  position: absolute;
  top: 0;
  width: 1000px;
}

.span-name:focus {
  text-decoration: none;
}

.endpoint-name {
  color: #808080;
}

.span-name:hover > .endpoint-name {
  color: #000;
}

.span-svc-name {
  padding: 0 0.25rem 0 0.5rem;
  font-size: 1.05em;
}

.span-svc-name.is-children-collapsed {
  font-weight: bold;
  font-style: italic;
}

.span-view {
  position: relative;
}

.span-row:hover .span-view {
  background-color: #f5f5f5;
  outline: 1px solid #ddd;
}

.span-row.is-matching-filter:hover .span-view {
  background-color: #fff3d7;
  outline: 1px solid #ddd;
}

.span-row.is-expanded .span-view {
  background: #f8f8f8;
  outline: 1px solid #ddd;
}

.span-row.is-expanded.is-matching-filter .span-view {
  background: #fff3d7;
  outline: 1px solid #ddd;
}

.span-row.is-expanded:hover .span-view {
  background: #eee;
}

.span-row.is-expanded.is-matching-filter:hover .span-view {
  background: #ffeccf;
}

.span-row.clipping-right .span-view::before {
  content: ' ';
  height: 100%;
  position: absolute;
  width: 6px;
  background-image: linear-gradient(to left, rgba(25, 25, 25, 0.25), rgba(32, 32, 32, 0));
  right: 0%;
  z-index: 1;
}

.SpanBarRow--errorIcon {
  background: #db2828;
  border-radius: 6.5px;
  color: #fff;
  font-size: 0.85em;
  margin-right: 0.25rem;
  padding: 1px;
}

.SpanBarRow--rpcColorMarker {
  border-radius: 6.5px;
  display: inline-block;
  font-size: 0.85em;
  height: 1em;
  margin-right: 0.25rem;
  padding: 1px;
  width: 1em;
  vertical-align: middle;
}

.SpanBarRow--arrowForwardIcon {
  vertical-align: middle;
}
