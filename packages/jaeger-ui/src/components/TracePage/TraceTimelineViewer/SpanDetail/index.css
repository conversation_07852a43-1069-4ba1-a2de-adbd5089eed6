/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.SpanDetail--divider {
  background: #ddd;
}

.SpanDetail--debugInfo {
  display: block;
  letter-spacing: 0.25px;
  margin: 0.5em 0 -0.75em;
  text-align: right;
}

.SpanDetail--debugLabel::before {
  color: #bbb;
  content: attr(data-label);
}

.SpanDetail--debugValue {
  background-color: inherit;
  border: none;
  color: #888;
  cursor: pointer;
}

.SpanDetail--debugValue:hover {
  color: #333;
}
.AccordianWarnings {
  background: #fafafa;
  border: 1px solid #e4e4e4;
  margin-bottom: 0.25rem;
}
.AccordianWarnings--header {
  background: #fff7e6;
  padding: 0.25rem 0.5rem;
}

.AccordianWarnings--header:hover {
  background: #ffe7ba;
}

.AccordianWarnings--header.is-open {
  border-bottom: 1px solid #e8e8e8;
}

.AccordianWarnings--label {
  color: #d36c08;
}
