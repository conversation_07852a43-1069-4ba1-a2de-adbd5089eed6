/*
Copyright (c) 2019 The Jaeger Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.ReferencesList--List {
  width: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
  background: #fff;
}
.ReferencesList {
  background: #fff;
  border: 1px solid #ddd;
  margin-bottom: 0.7em;
  max-height: 450px;
  overflow: auto;
}

.ReferencesList--itemContent {
  padding: 0.25rem 0.5rem;
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.ReferencesList--itemContent > a {
  width: 100%;
  display: inline-block;
}

.ReferencesList--Item:nth-child(2n) {
  background: #f5f5f5;
}

.SpanReference--debugInfo {
  letter-spacing: 0.25px;
  margin: 0.5em 0 0;
}

.SpanReference--debugLabel::before {
  color: #bbb;
  content: attr(data-label);
}

.SpanReference--debugLabel {
  margin: 0 5px 0 5px;
}
