/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.AccordianKeyValues--header {
  cursor: pointer;
  overflow: hidden;
  padding: 0.25em 0.1em;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.AccordianKeyValues--header:hover {
  background: #e8e8e8;
}

.AccordianKeyValues--header.is-empty {
  background: none;
  cursor: initial;
}

.AccordianKeyValues--header.is-high-contrast:not(.is-empty):hover {
  background: #ddd;
}

.AccordianKeyValues--emptyIcon {
  color: #aaa;
}

.AccordianKeyValues--summary {
  display: inline;
  list-style: none;
  padding: 0;
}

.AccordianKeyValues--summaryItem {
  display: inline;
  margin-left: 0.7em;
  padding-right: 0.5rem;
  border-right: 1px solid #ddd;
}

.AccordianKeyValues--summaryItem:last-child {
  padding-right: 0;
  border-right: none;
}

.AccordianKeyValues--summaryLabel {
  color: #777;
}

.AccordianKeyValues--summaryDelim {
  color: #bbb;
  padding: 0 0.2em;
}
