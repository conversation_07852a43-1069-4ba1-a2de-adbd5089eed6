/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.KeyValueTable {
  background: #fff;
  border: 1px solid #ddd;
  margin-bottom: 0.7em;
  max-height: 450px;
  overflow: auto;
}

.KeyValueTable--body {
  vertical-align: baseline;
}

.KeyValueTable--row > td {
  padding: 0.25rem 0.5rem;
}

.KeyValueTable--row:nth-child(2n) > td {
  background: #f5f5f5;
}

.KeyValueTable--keyColumn {
  color: #888;
  white-space: pre;
  width: 125px;
}

.KeyValueTable--valueColumn {
  position: relative;
}

.KeyValueTable--copyContainer {
  float: right;
  margin-left: 8px;
  white-space: nowrap;
}

.KeyValueTable--row:not(:hover) .KeyValueTable--copyContainer .KeyValueTable--copyIcon {
  visibility: hidden;
}

.KeyValueTable--row > td {
  padding: 0.25rem 0.5rem;
  vertical-align: top;
}

.KeyValueTable--linkIcon {
  vertical-align: middle;
  font-weight: bold;
}
