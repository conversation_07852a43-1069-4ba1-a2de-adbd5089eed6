/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.Ticks {
  pointer-events: none;
}

.Ticks--tick {
  position: absolute;
  height: 100%;
  width: 1px;
  background: #d8d8d8;
}

.Ticks--tick:last-child {
  width: 0;
}

.Ticks--tickLabel {
  left: 0.25rem;
  position: absolute;
  white-space: nowrap;
}

.Ticks--tickLabel.isEndAnchor {
  left: initial;
  right: 0.25rem;
}
