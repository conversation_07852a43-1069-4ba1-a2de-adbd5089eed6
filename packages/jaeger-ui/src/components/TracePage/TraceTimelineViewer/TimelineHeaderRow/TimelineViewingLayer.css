/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.TimelineViewingLayer {
  bottom: 0;
  cursor: vertical-text;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.TimelineViewingLayer--cursorGuide {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 1px;
  background-color: red;
}

.TimelineViewingLayer--dragged {
  position: absolute;
  top: 0;
  bottom: 0;
}

.TimelineViewingLayer--dragged.isDraggingLeft {
  border-left: 1px solid;
}

.TimelineViewingLayer--dragged.isDraggingRight {
  border-right: 1px solid;
}

.TimelineViewingLayer--dragged.isShiftDrag {
  background-color: rgba(68, 68, 255, 0.2);
  border-color: #44f;
}

.TimelineViewingLayer--dragged.isReframeDrag {
  background-color: rgba(255, 68, 68, 0.2);
  border-color: #f44;
}

.TimelineViewingLayer--fullOverlay {
  bottom: 0;
  cursor: col-resize;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  user-select: none;
}
