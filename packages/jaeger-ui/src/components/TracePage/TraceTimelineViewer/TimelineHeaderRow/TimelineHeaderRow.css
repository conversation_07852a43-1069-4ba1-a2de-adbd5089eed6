/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.TimelineHeaderRow {
  background: #ececec;
  border-bottom: 1px solid #ccc;
  height: 38px;
  line-height: 38px;
  position: fixed;
  width: 100%;
  z-index: 4;
}

.TimelineHeaderRow--title {
  flex: 1;
  overflow: hidden;
  margin: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}
