// Copyright (c) 2020 The Jaeger Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { getLocation, getUrl } from '.';

describe('TracePage/url', () => {
  const traceID = 'trace-id';
  const uiFind = 'ui-find';

  describe('getUrl', () => {
    it('includes traceID without uiFind', () => {
      expect(getUrl(traceID)).toBe(`/trace/${traceID}`);
    });

    it('includes traceID and uiFind', () => {
      expect(getUrl(traceID, uiFind)).toBe(`/trace/${traceID}?uiFind=${uiFind}`);
    });
  });

  describe('getLocation', () => {
    const state = {
      from: 'some-url',
    };

    it('passes provided state with correct pathname, without uiFind', () => {
      expect(getLocation(traceID, state)).toEqual({
        state,
        pathname: getUrl(traceID),
      });
    });

    it('passes provided state with correct pathname with uiFind', () => {
      expect(getLocation(traceID, state, uiFind)).toEqual({
        state,
        pathname: getUrl(traceID),
        search: `uiFind=${uiFind}`,
      });
    });
  });
});
