/*
Copyright (c) 2018 The Jaeger Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.TraceGraph--experimental {
  background-color: #a00;
  color: #fff;
  position: absolute;
  padding: 1px 15px;
}

.TraceGraph--graphWrapper {
  background: #f0f0f0;
  bottom: 0;
  cursor: move;
  left: 0;
  overflow: auto;
  position: absolute;
  right: 0;
  top: 0;
  transition: background 0.5s ease;
  display: flex;
}

.TraceGraph--graphWrapper.is-uiFind-mode {
  background: #ddd;
}

.TraceGraph--sidebar-container {
  background: #f4f4f4;
  display: flex;
  z-index: 1;
}

.TraceGraph--menu {
  border-left: 1px solid #e4e4e4;
  border-right: 1px solid #e4e4e4;
  cursor: pointer;
  padding: 0.8rem;
}

.TraceGraph--menu > li {
  list-style-type: none;
  text-align: center;
  padding-bottom: 0.3rem;
}

.TraceGraph--sidebar {
  cursor: default;
  box-shadow: -1px 0 rgba(0, 0, 0, 0.2);
}

.TraceGraph--help-content > div {
  margin-top: 1rem;
}

.TraceGraph--dag {
  stroke-width: 0.8;
}

/* DAG minimap */

.TraceGraph--miniMap {
  align-items: flex-end;
  bottom: 1rem;
  display: flex;
  left: 1rem;
  position: absolute;
  z-index: 1;
}

.TraceGraph--miniMap > .plexus-MiniMap--item {
  border: 1px solid #777;
  background: #999;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
  margin-right: 1rem;
  position: relative;
}

.TraceGraph--miniMap > .plexus-MiniMap--map {
  /* dynamic widht, height */
  box-sizing: content-box;
  cursor: not-allowed;
}

.TraceGraph--miniMap .plexus-MiniMap--mapActive {
  /* dynamic: width, height, transform */
  background: #ccc;
  position: absolute;
}

.TraceGraph--miniMap > .plexus-MiniMap--button {
  background: #ccc;
  color: #888;
  cursor: pointer;
  font-size: 1.6em;
  line-height: 0;
  padding: 0.1rem;
}

.TraceGraph--miniMap > .plexus-MiniMap--button:hover {
  background: #ddd;
}
