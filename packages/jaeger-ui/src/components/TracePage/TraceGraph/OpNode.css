/*
Copyright (c) 2018 The Jaeger Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.OpNode {
  width: 100%;
  background: #fff;
  cursor: pointer;
  white-space: nowrap;
  border-collapse: separate;
}

.OpNode--popoverContent {
  border: 1px solid #bbb;
}

.OpNode--vectorBorder {
  box-sizing: content-box;
  stroke: rgba(0, 0, 0, 0.4);
  stroke-width: 2;
}

.OpNode td,
.OpNode th {
  border: none;
}

.OpNode.is-ui-find-match {
  outline: inherit;
  outline-color: #fff3d7;
}

.OpNode--popover .OpNode.is-ui-find-match {
  outline: #fff3d7 solid 3px;
}

.OpNode--legendNode {
  background: #096dd9;
}

.OpNode--mode-time {
  background: #eee;
}

.OpNode--metricCell {
  text-align: right;
  padding: 0.3rem 0.5rem;
  background: rgba(255, 255, 255, 0.3);
}

.OpNode--labelCell {
  padding: 0.3rem 0.5rem 0.3rem 0.75rem;
}

.OpNode--popover .OpNode--copyIcon,
.OpNode:not(:hover) .OpNode--copyIcon {
  color: transparent;
  pointer-events: none;
}

.OpNode--service {
  display: flex;
  justify-content: space-between;
}

/* Tweak the popover aesthetics - unfortunate but necessary */

.OpNode--popover .ant-popover-inner-content {
  padding: 0;
  position: relative;
}
