/*
Copyright (c) 2020 The Jaeger Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.PopupSQL {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 3;
}

.PopupSQL--inner {
  position: absolute;
  left: 33%;
  right: 33%;
  top: 33%;
  bottom: 30%;
  margin: auto;
  background: rgb(255, 255, 255);
  z-index: 3;
  border-radius: 5px;
}

.PopupSQL--header {
  padding-top: 0.5em;
  display: flex;
  align-items: center;
  justify-content: center;
}

.PopupSQL--sqlContent {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 80%;
  height: 60%;
  color: rgb(0, 128, 128);
  font-family: monospace;
  font-size: 13px;
  outline-color: rgb(0, 128, 128);
  padding: 5px;
  resize: none;
}

.PopupSQL--closeButton {
  display: block;
  margin-left: auto;
  margin-right: auto;
  margin-top: 1.5em;
}
