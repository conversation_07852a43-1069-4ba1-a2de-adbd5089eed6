{"traceID": "006c3cf93508f205", "spans": [{"traceID": "006c3cf93508f205", "spanID": "006c3cf93508f205", "flags": 1, "operationName": "send", "references": [], "startTime": 100, "duration": 40, "tags": [{"key": "span.kind", "type": "string", "value": "producer"}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "006c3cf93508f205", "spanID": "2dc4b796e2127e32", "flags": 1, "operationName": "span with test", "startTime": 110, "duration": 10, "tags": [{"key": "span.kind", "type": "string", "value": "client"}, {"key": "http.method", "type": "string", "value": "POST"}, {"key": "app.test", "type": "string", "value": "1"}, {"key": "app.test-group2", "type": "string", "value": "group2"}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "006c3cf93508f205", "spanID": "2dc4b796e2127e32", "flags": 1, "operationName": "another span with test", "startTime": 110, "duration": 10, "tags": [{"key": "span.kind", "type": "string", "value": "client"}, {"key": "http.method", "type": "string", "value": "POST"}, {"key": "app.test", "type": "string", "value": "1"}, {"key": "app.test-group3", "type": "string", "value": "group2"}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "006c3cf93508f205", "spanID": "5d423585b4c63d48", "flags": 1, "operationName": "span with test1", "startTime": 500, "duration": 100, "tags": [{"key": "span.kind", "type": "string", "value": "client"}, {"key": "http.method", "type": "string", "value": "POST"}, {"key": "app.test1", "type": "string", "value": "1"}], "logs": [], "processID": "p1", "warnings": null}], "processes": {"p1": {"serviceName": "service-one", "tags": []}}, "warnings": null}