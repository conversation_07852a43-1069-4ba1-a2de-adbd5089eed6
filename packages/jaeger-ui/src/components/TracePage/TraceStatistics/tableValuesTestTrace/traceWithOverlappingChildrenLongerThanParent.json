{"traceID": "006c3cf93508f205", "spans": [{"traceID": "006c3cf93508f205", "spanID": "006c3cf93508f205", "flags": 1, "operationName": "send", "references": [], "startTime": 100, "duration": 40, "tags": [{"key": "span.kind", "type": "string", "value": "producer"}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "006c3cf93508f205", "spanID": "4570ab68826f43f9", "flags": 1, "operationName": "short child span", "references": [{"refType": "CHILD_OF", "traceID": "006c3cf93508f205", "spanID": "006c3cf93508f205"}], "startTime": 115, "duration": 10, "tags": [{"key": "span.kind", "type": "string", "value": "client"}, {"key": "http.method", "type": "string", "value": "POST"}], "logs": [], "processID": "p2", "warnings": null}, {"traceID": "006c3cf93508f205", "spanID": "2dc4b796e2127e32", "flags": 1, "operationName": "async task 1", "references": [{"refType": "CHILD_OF", "traceID": "006c3cf93508f205", "spanID": "006c3cf93508f205"}], "startTime": 200, "duration": 300, "tags": [{"key": "span.kind", "type": "string", "value": "client"}, {"key": "http.method", "type": "string", "value": "POST"}], "logs": [], "processID": "p2", "warnings": null}, {"traceID": "006c3cf93508f205", "spanID": "5d423585b4c63d48", "flags": 1, "operationName": "async task 2", "references": [{"refType": "CHILD_OF", "traceID": "006c3cf93508f205", "spanID": "006c3cf93508f205"}], "startTime": 300, "duration": 100, "tags": [{"key": "span.kind", "type": "string", "value": "client"}, {"key": "http.method", "type": "string", "value": "POST"}], "logs": [], "processID": "p2", "warnings": null}], "processes": {"p1": {"serviceName": "service-one", "tags": []}, "p2": {"serviceName": "service-two", "tags": []}}, "warnings": null}