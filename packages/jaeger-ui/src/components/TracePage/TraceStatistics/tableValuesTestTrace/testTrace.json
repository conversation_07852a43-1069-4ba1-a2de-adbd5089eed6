{"traceID": "trace-123", "spans": [{"traceID": "trace-123", "spanID": "span-1", "flags": 1, "operationName": "op1", "startTime": 1542666452979000, "duration": 390000, "references": [], "tags": [{"key": "span.kind", "type": "string", "value": "server"}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "trace-123", "spanID": "span-2", "flags": 1, "operationName": "op2", "startTime": 1542666453104000, "duration": 33000, "references": [{"refType": "CHILD_OF", "traceID": "trace-123", "spanID": "span-1"}], "tags": [{"key": "span.kind", "type": "string", "value": "client"}, {"key": "error", "type": "bool", "value": "true"}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "trace-123", "spanID": "span-2_1", "flags": 1, "operationName": "op2", "startTime": 1542666453229000, "duration": 37000, "references": [{"refType": "CHILD_OF", "traceID": "trace-123", "spanID": "span-1"}], "tags": [{"key": "span.kind", "type": "string", "value": "client"}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "trace-123", "spanID": "span-3", "flags": 1, "operationName": "op3", "startTime": 1542666453159000, "duration": 66000, "references": [{"refType": "CHILD_OF", "traceID": "trace-123", "spanID": "span-1"}], "tags": [{"key": "span.kind", "type": "string", "value": "client"}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "trace-123", "spanID": "span-4", "flags": 1, "operationName": "op1", "startTime": 1542666453179000, "duration": 20000, "references": [{"refType": "CHILD_OF", "traceID": "trace-123", "spanID": "span-3"}], "tags": [{"key": "span.kind", "type": "string", "value": "server"}], "logs": [], "processID": "p2", "warnings": null}, {"traceID": "trace-123", "spanID": "span-5", "flags": 1, "operationName": "op2", "startTime": 1542666453180000, "duration": 18000, "references": [{"refType": "CHILD_OF", "traceID": "trace-123", "spanID": "span-4"}], "tags": [{"key": "db.type", "type": "string", "value": "sql"}], "logs": [], "processID": "p2", "warnings": null}, {"traceID": "trace-123", "spanID": "span-6", "flags": 1, "operationName": "op4", "startTime": 1542666453279000, "duration": 20000, "references": [{"refType": "CHILD_OF", "traceID": "trace-123", "spanID": "span-1"}], "tags": [{"key": "span.kind", "type": "string", "value": "producer"}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "trace-123", "spanID": "span-7", "flags": 1, "operationName": "op3", "startTime": 1542666453779000, "duration": 200000, "references": [{"refType": "FOLLOWS_FROM", "traceID": "trace-123", "spanID": "span-6"}], "tags": [{"key": "span.kind", "type": "string", "value": "consumer"}], "logs": [], "processID": "p2", "warnings": null}, {"traceID": "trace-123", "spanID": "span-12", "flags": 1, "operationName": "op6", "startTime": 1542666453309000, "duration": 10000, "references": [{"refType": "CHILD_OF", "traceID": "trace-123", "spanID": "span-1"}], "tags": [], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "trace-123", "spanID": "span-13", "flags": 1, "operationName": "op7", "startTime": 1542666453310000, "duration": 9000, "references": [{"refType": "CHILD_OF", "traceID": "trace-123", "spanID": "span-12"}], "tags": [], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "trace-123", "spanID": "span-14", "flags": 1, "operationName": "op7", "startTime": 1542666453311000, "duration": 8000, "references": [{"refType": "CHILD_OF", "traceID": "trace-123", "spanID": "span-12"}], "tags": [], "logs": [], "processID": "p1", "warnings": null}], "processes": {"p1": {"serviceName": "service1", "tags": [{"key": "hostname", "type": "string", "value": "foobar.org"}]}, "p2": {"serviceName": "service2", "tags": [{"key": "hostname", "type": "string", "value": "foobar.org"}]}}, "warnings": null}