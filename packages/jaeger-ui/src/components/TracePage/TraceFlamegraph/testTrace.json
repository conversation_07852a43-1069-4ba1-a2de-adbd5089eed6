{"data": {"services": [{"name": "load-generator", "numberOfSpans": 2}, {"name": "ride-sharing-app", "numberOfSpans": 2}], "spans": [{"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "487f03177ef9cf89", "flags": 1, "operationName": "OrderVehicle", "references": [], "startTime": 1660049722779884, "duration": 1181596, "tags": [{"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/otel/sdk/tracer"}, {"key": "pyroscope.profile.baseline.url", "type": "string", "value": "http://localhost:4040/comparison?from=1660046122&leftFrom=1660046122&leftQuery=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&leftUntil=1660049723&query=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&rightFrom=1660046122&rightQuery=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.diff.url", "type": "string", "value": "http://localhost:4040/comparison-diff?from=1660046122&leftFrom=1660046122&leftQuery=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&leftUntil=1660049723&query=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&rightFrom=1660046122&rightQuery=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.id", "type": "string", "value": "487f03177ef9cf89"}, {"key": "pyroscope.profile.url", "type": "string", "value": "http://localhost:4040/?from=1660049722779903100&query=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&until=1660049723961431700"}, {"key": "vehicle", "type": "string", "value": "bike"}], "logs": [], "processID": "p2", "warnings": [], "process": {"serviceName": "load-generator", "tags": []}, "relativeStartTime": 0, "depth": 0, "hasChildren": true}, {"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "d81ccf159d71bc66", "flags": 1, "operationName": "HTTP GET", "references": [{"refType": "CHILD_OF", "traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "487f03177ef9cf89", "span": {"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "487f03177ef9cf89", "flags": 1, "operationName": "OrderVehicle", "references": [], "startTime": 1660049722779884, "duration": 1181596, "tags": [{"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/otel/sdk/tracer"}, {"key": "pyroscope.profile.baseline.url", "type": "string", "value": "http://localhost:4040/comparison?from=1660046122&leftFrom=1660046122&leftQuery=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&leftUntil=1660049723&query=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&rightFrom=1660046122&rightQuery=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.diff.url", "type": "string", "value": "http://localhost:4040/comparison-diff?from=1660046122&leftFrom=1660046122&leftQuery=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&leftUntil=1660049723&query=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&rightFrom=1660046122&rightQuery=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.id", "type": "string", "value": "487f03177ef9cf89"}, {"key": "pyroscope.profile.url", "type": "string", "value": "http://localhost:4040/?from=1660049722779903100&query=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&until=1660049723961431700"}, {"key": "vehicle", "type": "string", "value": "bike"}], "logs": [], "processID": "p2", "warnings": [], "process": {"serviceName": "load-generator", "tags": []}, "relativeStartTime": 0, "depth": 0, "hasChildren": true}}], "startTime": 1660049722993018, "duration": 968334, "tags": [{"key": "http.flavor", "type": "string", "value": "1.1"}, {"key": "http.host", "type": "string", "value": "ap-south:5000"}, {"key": "http.method", "type": "string", "value": "GET"}, {"key": "http.scheme", "type": "string", "value": "http"}, {"key": "http.status_code", "type": "int64", "value": 200}, {"key": "http.url", "type": "string", "value": "http://ap-south:5000/bike"}, {"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"}, {"key": "otel.library.version", "type": "string", "value": "semver:0.27.0"}, {"key": "span.kind", "type": "string", "value": "client"}], "logs": [], "processID": "p2", "warnings": [], "process": {"serviceName": "load-generator", "tags": []}, "relativeStartTime": 213134, "depth": 1, "hasChildren": true}, {"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "3d4670fa91902216", "flags": 1, "operationName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "references": [{"refType": "CHILD_OF", "traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "d81ccf159d71bc66", "span": {"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "d81ccf159d71bc66", "flags": 1, "operationName": "HTTP GET", "references": [{"refType": "CHILD_OF", "traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "487f03177ef9cf89", "span": {"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "487f03177ef9cf89", "flags": 1, "operationName": "OrderVehicle", "references": [], "startTime": 1660049722779884, "duration": 1181596, "tags": [{"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/otel/sdk/tracer"}, {"key": "pyroscope.profile.baseline.url", "type": "string", "value": "http://localhost:4040/comparison?from=1660046122&leftFrom=1660046122&leftQuery=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&leftUntil=1660049723&query=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&rightFrom=1660046122&rightQuery=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.diff.url", "type": "string", "value": "http://localhost:4040/comparison-diff?from=1660046122&leftFrom=1660046122&leftQuery=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&leftUntil=1660049723&query=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&rightFrom=1660046122&rightQuery=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.id", "type": "string", "value": "487f03177ef9cf89"}, {"key": "pyroscope.profile.url", "type": "string", "value": "http://localhost:4040/?from=1660049722779903100&query=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&until=1660049723961431700"}, {"key": "vehicle", "type": "string", "value": "bike"}], "logs": [], "processID": "p2", "warnings": [], "process": {"serviceName": "load-generator", "tags": []}, "relativeStartTime": 0, "depth": 0, "hasChildren": true}}], "startTime": 1660049722993018, "duration": 968334, "tags": [{"key": "http.flavor", "type": "string", "value": "1.1"}, {"key": "http.host", "type": "string", "value": "ap-south:5000"}, {"key": "http.method", "type": "string", "value": "GET"}, {"key": "http.scheme", "type": "string", "value": "http"}, {"key": "http.status_code", "type": "int64", "value": 200}, {"key": "http.url", "type": "string", "value": "http://ap-south:5000/bike"}, {"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"}, {"key": "otel.library.version", "type": "string", "value": "semver:0.27.0"}, {"key": "span.kind", "type": "string", "value": "client"}], "logs": [], "processID": "p2", "warnings": [], "process": {"serviceName": "load-generator", "tags": []}, "relativeStartTime": 213134, "depth": 1, "hasChildren": true}}], "startTime": 1660049722993895, "duration": 967066, "tags": [{"key": "http.flavor", "type": "string", "value": "1.1"}, {"key": "http.host", "type": "string", "value": "ap-south:5000"}, {"key": "http.method", "type": "string", "value": "GET"}, {"key": "http.scheme", "type": "string", "value": "http"}, {"key": "http.server_name", "type": "string", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "http.target", "type": "string", "value": "/bike"}, {"key": "http.user_agent", "type": "string", "value": "Go-http-client/1.1"}, {"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "net.host.name", "type": "string", "value": "ap-south"}, {"key": "net.host.port", "type": "int64", "value": 5000}, {"key": "net.peer.ip", "type": "string", "value": "***********"}, {"key": "net.peer.port", "type": "int64", "value": 55132}, {"key": "net.transport", "type": "string", "value": "ip_tcp"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"}, {"key": "otel.library.version", "type": "string", "value": "semver:0.27.0"}, {"key": "pyroscope.profile.baseline.url", "type": "string", "value": "http://localhost:4040/comparison?from=1660046122&leftFrom=1660046122&leftQuery=ride-sharing-app.cpu%7Bspan_name%3D%22BikeHandler%22%2Cregion%3D%22ap-south%22%7D&leftUntil=1660049723&query=ride-sharing-app.cpu%7Bspan_name%3D%22BikeHandler%22%2Cregion%3D%22ap-south%22%7D&rightFrom=1660046122&rightQuery=ride-sharing-app.cpu%7Bprofile_id%3D%223d4670fa91902216%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.diff.url", "type": "string", "value": "http://localhost:4040/comparison-diff?from=1660046122&leftFrom=1660046122&leftQuery=ride-sharing-app.cpu%7Bspan_name%3D%22BikeHandler%22%2Cregion%3D%22ap-south%22%7D&leftUntil=1660049723&query=ride-sharing-app.cpu%7Bspan_name%3D%22BikeHandler%22%2Cregion%3D%22ap-south%22%7D&rightFrom=1660046122&rightQuery=ride-sharing-app.cpu%7Bprofile_id%3D%223d4670fa91902216%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.id", "type": "string", "value": "3d4670fa91902216"}, {"key": "pyroscope.profile.url", "type": "string", "value": "http://localhost:4040/?from=1660049722993922900&query=ride-sharing-app.cpu%7Bprofile_id%3D%223d4670fa91902216%22%7D&until=1660049723960563300"}, {"key": "span.kind", "type": "string", "value": "server"}], "logs": [], "processID": "p1", "warnings": [], "process": {"serviceName": "ride-sharing-app", "tags": []}, "relativeStartTime": 214011, "depth": 2, "hasChildren": true}, {"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "7bc101ef51dbc40c", "flags": 1, "operationName": "FindNearestVehicle", "references": [{"refType": "CHILD_OF", "traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "3d4670fa91902216", "span": {"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "3d4670fa91902216", "flags": 1, "operationName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "references": [{"refType": "CHILD_OF", "traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "d81ccf159d71bc66", "span": {"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "d81ccf159d71bc66", "flags": 1, "operationName": "HTTP GET", "references": [{"refType": "CHILD_OF", "traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "487f03177ef9cf89", "span": {"traceID": "84b3fad94c6112edd6af3347d06f6f1d", "spanID": "487f03177ef9cf89", "flags": 1, "operationName": "OrderVehicle", "references": [], "startTime": 1660049722779884, "duration": 1181596, "tags": [{"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/otel/sdk/tracer"}, {"key": "pyroscope.profile.baseline.url", "type": "string", "value": "http://localhost:4040/comparison?from=1660046122&leftFrom=1660046122&leftQuery=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&leftUntil=1660049723&query=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&rightFrom=1660046122&rightQuery=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.diff.url", "type": "string", "value": "http://localhost:4040/comparison-diff?from=1660046122&leftFrom=1660046122&leftQuery=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&leftUntil=1660049723&query=load-generator.cpu%7Bspan_name%3D%22OrderVehicle%22%7D&rightFrom=1660046122&rightQuery=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.id", "type": "string", "value": "487f03177ef9cf89"}, {"key": "pyroscope.profile.url", "type": "string", "value": "http://localhost:4040/?from=1660049722779903100&query=load-generator.cpu%7Bprofile_id%3D%22487f03177ef9cf89%22%7D&until=1660049723961431700"}, {"key": "vehicle", "type": "string", "value": "bike"}], "logs": [], "processID": "p2", "warnings": [], "process": {"serviceName": "load-generator", "tags": []}, "relativeStartTime": 0, "depth": 0, "hasChildren": true}}], "startTime": 1660049722993018, "duration": 968334, "tags": [{"key": "http.flavor", "type": "string", "value": "1.1"}, {"key": "http.host", "type": "string", "value": "ap-south:5000"}, {"key": "http.method", "type": "string", "value": "GET"}, {"key": "http.scheme", "type": "string", "value": "http"}, {"key": "http.status_code", "type": "int64", "value": 200}, {"key": "http.url", "type": "string", "value": "http://ap-south:5000/bike"}, {"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"}, {"key": "otel.library.version", "type": "string", "value": "semver:0.27.0"}, {"key": "span.kind", "type": "string", "value": "client"}], "logs": [], "processID": "p2", "warnings": [], "process": {"serviceName": "load-generator", "tags": []}, "relativeStartTime": 213134, "depth": 1, "hasChildren": true}}], "startTime": 1660049722993895, "duration": 967066, "tags": [{"key": "http.flavor", "type": "string", "value": "1.1"}, {"key": "http.host", "type": "string", "value": "ap-south:5000"}, {"key": "http.method", "type": "string", "value": "GET"}, {"key": "http.scheme", "type": "string", "value": "http"}, {"key": "http.server_name", "type": "string", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "http.target", "type": "string", "value": "/bike"}, {"key": "http.user_agent", "type": "string", "value": "Go-http-client/1.1"}, {"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "net.host.name", "type": "string", "value": "ap-south"}, {"key": "net.host.port", "type": "int64", "value": 5000}, {"key": "net.peer.ip", "type": "string", "value": "***********"}, {"key": "net.peer.port", "type": "int64", "value": 55132}, {"key": "net.transport", "type": "string", "value": "ip_tcp"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"}, {"key": "otel.library.version", "type": "string", "value": "semver:0.27.0"}, {"key": "pyroscope.profile.baseline.url", "type": "string", "value": "http://localhost:4040/comparison?from=1660046122&leftFrom=1660046122&leftQuery=ride-sharing-app.cpu%7Bspan_name%3D%22BikeHandler%22%2Cregion%3D%22ap-south%22%7D&leftUntil=1660049723&query=ride-sharing-app.cpu%7Bspan_name%3D%22BikeHandler%22%2Cregion%3D%22ap-south%22%7D&rightFrom=1660046122&rightQuery=ride-sharing-app.cpu%7Bprofile_id%3D%223d4670fa91902216%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.diff.url", "type": "string", "value": "http://localhost:4040/comparison-diff?from=1660046122&leftFrom=1660046122&leftQuery=ride-sharing-app.cpu%7Bspan_name%3D%22BikeHandler%22%2Cregion%3D%22ap-south%22%7D&leftUntil=1660049723&query=ride-sharing-app.cpu%7Bspan_name%3D%22BikeHandler%22%2Cregion%3D%22ap-south%22%7D&rightFrom=1660046122&rightQuery=ride-sharing-app.cpu%7Bprofile_id%3D%223d4670fa91902216%22%7D&rightUntil=1660049723&until=1660049723"}, {"key": "pyroscope.profile.id", "type": "string", "value": "3d4670fa91902216"}, {"key": "pyroscope.profile.url", "type": "string", "value": "http://localhost:4040/?from=1660049722993922900&query=ride-sharing-app.cpu%7Bprofile_id%3D%223d4670fa91902216%22%7D&until=1660049723960563300"}, {"key": "span.kind", "type": "string", "value": "server"}], "logs": [], "processID": "p1", "warnings": [], "process": {"serviceName": "ride-sharing-app", "tags": []}, "relativeStartTime": 214011, "depth": 2, "hasChildren": true}}], "startTime": 1660049722994061, "duration": 966333, "tags": [{"key": "internal.span.format", "type": "string", "value": "jaeger"}, {"key": "otel.library.name", "type": "string", "value": "go.opentelemetry.io/otel/sdk/tracer"}, {"key": "vehicle", "type": "string", "value": "bike"}], "logs": [], "processID": "p1", "warnings": [], "process": {"serviceName": "ride-sharing-app", "tags": []}, "relativeStartTime": 214177, "depth": 3, "hasChildren": false}], "traceID": "84b3fad94c6112edd6af3347d06f6f1d", "traceName": "load-generator: OrderVehicle", "processes": {"p1": {"serviceName": "ride-sharing-app", "tags": []}, "p2": {"serviceName": "load-generator", "tags": []}}, "duration": 1181596, "startTime": 1660049722779884, "endTime": 1660049723961480}, "id": "84b3fad94c6112edd6af3347d06f6f1d", "state": "FETCH_DONE"}