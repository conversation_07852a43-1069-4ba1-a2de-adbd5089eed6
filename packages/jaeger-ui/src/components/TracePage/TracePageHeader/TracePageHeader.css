/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.TracePageHeader > :first-child {
  border-bottom: 1px solid #e8e8e8;
}

.TracePageHeader > :nth-child(2) {
  background-color: #eee;
  border-bottom: 1px solid #e4e4e4;
}

.TracePageHeader > :last-child {
  background-color: #f8f8f8;
  border-bottom: 1px solid #ccc;
}

/* boost the specificity for cases with only one row -- bg should be white */
.TracePageHeader > .TracePageHeader--titleRow {
  align-items: center;
  background-color: #fff;
  display: flex;
}

.TracePageHeader--back {
  align-items: center;
  align-self: stretch;
  background-color: #fafafa;
  border-bottom: 1px solid #ddd;
  border-right: 1px solid #ddd;
  color: inherit;
  display: flex;
  font-size: 1.4rem;
  padding: 0 1rem;
  margin-bottom: -1px;
}

.TracePageHeader--back:hover {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.TracePageHeader--titleLink {
  align-items: center;
  color: var(--tx-color-title);
  display: flex;
  flex: 1;
}

.TracePageHeader--titleLink:hover * {
  text-decoration: underline;
}

.TracePageHeader--titleLink:hover > *,
.TracePageHeader--titleLink:hover small {
  text-decoration: none;
}

.TracePageHeader--detailToggle {
  font-size: 2rem;
  margin: 0 12px;
  transition: transform 0.07s ease-out;
}

.TracePageHeader--detailToggle.is-expanded {
  transform: rotate(90deg);
}

.TracePageHeader--title {
  color: inherit;
  flex: 1;
  font-size: 1.7em;
  line-height: 1em;
  margin: 0 0 0 0.5em;
  padding: 0.5em 0;
}

.TracePageHeader--title.is-collapsible {
  margin-left: 0;
}

.TracePageHeader--overviewItems {
  border-bottom: 1px solid #e4e4e4;
  padding: 0.25rem 0.5rem;
}

.TracePageHeader--overviewItem--valueDetail {
  color: #aaa;
}

.TracePageHeader--overviewItem--value:hover > .TracePageHeader--overviewItem--valueDetail {
  color: unset;
}

.TracePageHeader--archiveIcon {
  font-size: 1.78em;
  margin-right: 0.15em;
}
