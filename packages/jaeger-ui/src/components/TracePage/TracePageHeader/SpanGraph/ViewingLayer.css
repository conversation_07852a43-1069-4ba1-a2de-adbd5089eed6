/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.ViewingLayer {
  cursor: vertical-text;
  position: relative;
  z-index: 1;
}

.ViewingLayer--graph {
  border: 1px solid #999;
  /* need !important here to overcome something from semantic UI */
  overflow: visible !important;
  position: relative;
  transform-origin: 0 0;
  width: 100%;
}

.ViewingLayer--inactive {
  fill: rgba(214, 214, 214, 0.5);
}

.ViewingLayer--cursorGuide {
  stroke: #f44;
  stroke-width: 1;
}

.ViewingLayer--draggedShift {
  fill-opacity: 0.2;
}

.ViewingLayer--draggedShift.isShiftDrag,
.ViewingLayer--draggedEdge.isShiftDrag {
  fill: #44f;
}

.ViewingLayer--draggedShift.isReframeDrag,
.ViewingLayer--draggedEdge.isReframeDrag {
  fill: #f44;
}

.ViewingLayer--fullOverlay {
  bottom: 0;
  cursor: col-resize;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  user-select: none;
}

.ViewingLayer--resetZoom {
  display: none;
  position: absolute;
  right: 1%;
  top: 10%;
  z-index: 1;
}

.ViewingLayer:hover > .ViewingLayer--resetZoom {
  display: unset;
}
