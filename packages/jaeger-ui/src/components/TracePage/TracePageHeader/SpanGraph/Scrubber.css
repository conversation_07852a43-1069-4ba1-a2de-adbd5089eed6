/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.Scrubber--handleExpansion {
  cursor: col-resize;
  fill-opacity: 0;
  fill: #44f;
}

.Scrubber.isDragging .Scrubber--handleExpansion,
.Scrubber--handles:hover > .Scrubber--handleExpansion {
  fill-opacity: 1;
}

.Scrubber--handle {
  cursor: col-resize;
  fill: #555;
}

.Scrubber.isDragging .Scrubber--handle,
.Scrubber--handles:hover > .Scrubber--handle {
  fill: #44f;
}

.Scrubber--line {
  pointer-events: none;
  stroke: #555;
}

.Scrubber.isDragging > .Scrubber--line,
.Scrubber--handles:hover + .Scrubber--line {
  stroke: #44f;
}
