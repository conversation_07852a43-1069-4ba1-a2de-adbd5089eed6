/*
Copyright (c) 2025 The Jaeger Authors.
SPDX-License-Identifier: Apache-2.0
*/

.NodeContent--actionsItemIconWrapper {
  flex: none;
  height: 16px;
  width: 16px;
}

.NodeContent--actionsItemIconWrapper > * {
  height: 16px;
  position: absolute;
  width: 16px;
}

.NodeContent--actionsItemIconWrapper > * > .ant-checkbox {
  vertical-align: unset;
  top: unset;
}

.NodeContent--actionsItemText {
  font-size: 0.9em;
  line-height: normal;
  margin-left: 0.5em;
}

.NodeContent--actionsItem {
  align-items: center;
  display: flex;
  padding: 0.5em;
}

.NodeContent--actionsItem:not(:hover) {
  color: inherit;
}
