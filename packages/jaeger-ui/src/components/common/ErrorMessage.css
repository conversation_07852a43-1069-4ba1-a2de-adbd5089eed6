/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.ErrorMessage {
  position: relative;
  word-break: break-word;
  word-wrap: break-word;
}

.ErrorMessage--msg {
  color: #c00;
  margin: 0;
  padding-bottom: 1rem;
}

.ErrorMessage--details {
  background: #f5f5f5;
  border: 1px solid #e6e6e6;
  overflow: auto;
}

.ErrorMessage--detailsTable {
  min-width: 100%;
}

.ErrorMessage--detailItem:nth-child(2n) > .ErrorMessage--attr,
.ErrorMessage--detailItem:nth-child(2n) > .ErrorMessage--value {
  background-color: #f0f0f0;
  border-bottom: 1px solid #e8e8e8;
  border-top: 1px solid #e8e8e8;
}

/* dont have extra borders on the bottom row */
.ErrorMessage--detailItem:last-child > .ErrorMessage--attr,
.ErrorMessage--detailItem:last-child > .ErrorMessage--value {
  border-bottom: none;
}

.ErrorMessage--attr {
  color: rgba(0, 0, 0, 0.45);
  padding: 0.5em;
  vertical-align: top;
  white-space: nowrap;
}

.ErrorMessage--value {
  color: rgba(0, 0, 0, 0.8);
  font-family: monospace;
  padding: 0.5em;
  white-space: pre;
}
