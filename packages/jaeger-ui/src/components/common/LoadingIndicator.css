/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

@keyframes LoadingIndicator--colorAnim {
  /*
  rgb(0, 128, 128) == teal
  rgba(0, 128, 128, 0.3) == #bedfdf
  */
  from {
    color: #bedfdf;
  }
  to {
    color: teal;
  }
}

@keyframes LoadingIndicator--spin {
  100% {
    transform: rotate(360deg);
  }
}

.LoadingIndicator {
  animation:
    LoadingIndicator--colorAnim 1s infinite alternate,
    LoadingIndicator--spin 1.2s infinite linear;
  /* outline / stroke the loading indicator */
  text-shadow:
    -0.5px 0 rgba(0, 128, 128, 0.6),
    0 0.5px rgba(0, 128, 128, 0.6),
    0.5px 0 rgba(0, 128, 128, 0.6),
    0 -0.5px rgba(0, 128, 128, 0.6);

  width: 42px;
  height: 42px;
}

.LoadingIndicator.is-centered {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.LoadingIndicator.is-vcentered {
  display: block;
  margin: auto;
}

.LoadingIndicator.is-small {
  width: 32px;
  height: 32px;
}
