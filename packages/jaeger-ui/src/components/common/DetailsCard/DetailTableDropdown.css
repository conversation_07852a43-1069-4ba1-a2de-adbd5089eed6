/*
Copyright (c) 2019 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.DetailTableDropdown--Footer {
  background: white;
  border: 1px solid #ccc;
  display: flex;
  justify-content: space-between;
  padding: 0.3em;
}

.DetailTableDropdown--Footer--CancelConfirm {
  display: flex;
}

.DetailTableDropdown--Btn {
  align-items: center;
  border-radius: 0;
  color: #ddd;
  display: flex;
  padding: 0.3em 0.7em 0.3em 0.6em;
}

.DetailTableDropdown--Btn ~ .DetailTableDropdown--Btn {
  margin-left: 0.3em;
}

.DetailTableDropdown--Btn > *:not(:first-child) {
  margin-left: 0.3em;
}

.DetailTableDropdown--Btn.Apply {
  background: #4c21ce;
}

.DetailTableDropdown--Btn.Cancel {
  background: #007272;
}

.DetailTableDropdown--Btn.Clear {
  background: #ff2626;
}

.DetailTableDropdown--Tooltip {
  max-width: unset;
  white-space: nowrap;
}

.DetailTableDropdown--Tooltip--Body {
  display: flex;
  flex-direction: column;
}
