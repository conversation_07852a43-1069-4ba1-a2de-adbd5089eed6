/*
Copyright (c) 2020 The Jaeger Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.DetailsCard {
  display: flex;
  flex-direction: column;
  padding: 0.5em;
}

.DetailsCard--ButtonHeaderWrapper {
  display: flex;
}

.DetailsCard--Collapser {
  border: none;
  background: transparent;
  cursor: pointer;
  flex: 0;
  font-size: 1.5rem;
  transition: transform 0.25s ease-out;
}

.DetailsCard--Collapser:focus {
  outline: none;
}

.DetailsCard--Collapser.is-collapsed {
  transform: rotate(-90deg);
}

.DetailsCard--HeaderWrapper {
  flex-grow: 1;
}

.DetailsCard--Header {
  border-bottom: solid 1px #ddd;
  box-shadow: 0px 5px 5px -5px rgba(0, 0, 0, 0.3);
  font-size: 1.5em;
  padding: 0.1em 0.3em;
}

.DetailsCard--Description {
  margin-bottom: 0;
  max-width: 90%;
}

.DetailsCard--DetailsWrapper {
  overflow: scroll;
  margin-top: 0.5em;
  transition: max-height 0.25s ease-out;
  max-height: 60vh;
}

.DetailsCard--DetailsWrapper.is-collapsed {
  max-height: 0px;
}

.DetailsCard--DetailsWrapper th {
  min-width: 65px;
}
