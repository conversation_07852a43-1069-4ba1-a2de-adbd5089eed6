/*
Copyright (c) 2019 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.FilteredList--ListItem {
  align-items: center;
  cursor: pointer;
  display: flex;
  padding: 0.5em 5em 0.5em 0.5em;
  white-space: nowrap;
  min-width: 100%;
}

.FilteredList--ListItem.is-striped {
  background: linear-gradient(to right, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0));
}

.FilteredList--ListItem.is-striped:hover,
.FilteredList--ListItem:hover {
  background: linear-gradient(
    to right,
    rgba(135, 232, 222, 0.1),
    rgba(135, 232, 222, 0.2) 70%,
    rgba(135, 232, 222, 0)
  );
  color: var(--tx-color-title);
}

.FilteredList--ListItem.is-selected {
  background: linear-gradient(to right, #87e8de, #87e8deaa 70%, #87e8de00);
  color: var(--tx-color-title);
}

.FilteredList--ListItem.is-striped.is-focused,
.FilteredList--ListItem.is-focused {
  background: linear-gradient(
    to right,
    rgba(135, 232, 222, 0.2),
    rgba(17, 153, 153, 0.2) 70%,
    rgba(17, 153, 153, 0)
  );
  color: var(--tx-color-title);
}

.FilteredList--ListItem mark {
  background: none;
  color: #eb2f96;
  font-weight: 500;
  padding: 0;
}

.FilteredList--ListItem--Checkbox {
  margin-right: 0.5em;
}
