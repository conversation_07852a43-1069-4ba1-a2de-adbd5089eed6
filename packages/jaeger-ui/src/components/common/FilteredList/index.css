/*
Copyright (c) 2019 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

.FilteredList--list {
  background: #fafafa;
}

.FilteredList--filterCheckbox {
  margin: 0 0.5em;
}

.FilteredList--filterWrapper {
  align-items: center;
  background: white;
  box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.3);
  display: flex;
  position: relative;
  z-index: 10;
}

.FilteredList--inputWrapper {
  align-items: center;
  background: white;
  box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-grow: 1;
  position: relative;
  z-index: 10;
}

.FilteredList--filterIcon {
  font-size: 1.3em;
  margin: 0 0.25em 0 0.65em;
  position: absolute;
}

.FilteredList--filterIcon.isMulti {
  margin-left: 2em;
}

.FilteredList--filterInput {
  border: none;
  flex: 1;
  height: auto;
  padding: 0.5em 0.3em 0.5em 2.5em;
}

.FilteredList--filterInput:focus {
  box-shadow: inset 0px 0px 3px 1px #6aa;
  outline: 1px solid #199;
  outline-offset: -1px;
}
