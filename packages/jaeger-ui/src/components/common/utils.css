/*
Copyright (c) 2017 Uber Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

:root {
  --nav-height: 48px;
}

.u-width-100 {
  width: 100%;
}

.u-flex-1 {
  flex: 1;
}

.u-mt-vast {
  margin-top: 13rem;
}

.u-cursor-pointer {
  cursor: pointer;
}

.u-tx-muted {
  color: var(--tx-color-muted);
}

.u-tx-inherit {
  color: inherit;
}

.u-tx-ellipsis {
  text-overflow: ellipsis;
}

.u-align-icon {
  margin: -0.2rem 0.25rem 0 0;
}

.u-simple-card {
  background-color: #f5f5f5;
  border: 1px solid #e6e6e6;
  padding: 1rem;
}

/* simple-scrollbars */
.u-simple-scrollbars::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 8px;
  height: 8px;
}

.u-simple-scrollbars::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
}

.u-simple-scrollbars::-webkit-scrollbar-track:vertical {
  border-left: 1px solid rgba(0, 0, 0, 0.13);
}

.u-simple-scrollbars::-webkit-scrollbar-track:horizontal {
  border-top: 1px solid rgba(0, 0, 0, 0.13);
}

.u-simple-scrollbars::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
}

.u-simple-scrollbars:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.35);
}

.u-simple-scrollbars::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.15);
}

/* Remove the padding around ant-design's popover, unfortunate but very handy */
.u-rm-popover-title-padding .ant-popover-title,
.u-rm-popover-content-padding .ant-popover-inner-content {
  padding: 0;
}

/* minimap for plexus */

.u-miniMap {
  align-items: flex-end;
  bottom: 1rem;
  display: flex;
  left: 1rem;
  position: absolute;
  z-index: 1;
}

.u-miniMap > .plexus-MiniMap--item {
  background: #999;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
  margin-right: 1rem;
  position: relative;
}

/* Add the border as a pseudo element so the inner content doesn't mask it */
.u-miniMap > .plexus-MiniMap--item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  outline: 1px solid #777;
}

.u-miniMap > .plexus-MiniMap--map {
  /* dynamic widht, height */
  box-sizing: content-box;
  cursor: not-allowed;
}

.u-miniMap .plexus-MiniMap--mapActive {
  /* dynamic: width, height, transform */
  background: #ccc;
  position: absolute;
}

.u-miniMap > .plexus-MiniMap--button {
  background: #ccc;
  color: #444;
  cursor: pointer;
  font-size: 1.6em;
  line-height: 0;
  padding: 0.1rem;
}

.u-miniMap > .plexus-MiniMap--button:hover {
  background: #ddd;
}
