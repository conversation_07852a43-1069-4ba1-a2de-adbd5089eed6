// Copyright (c) 2023 The Jaeger Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

const path = require('path');

// Simple Jest transform that stubs out CSS files and returns the file name as the mock content for other file types.
module.exports = {
  process(src, filename) {
    const fileExtension = path.extname(filename);
    if (fileExtension === 'css') {
      return { code: 'module.exports = "";' };
    }
    return { code: `module.exports = ${JSON.stringify(path.basename(filename))};` };
  },
};
